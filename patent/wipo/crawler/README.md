# WIPO Patent Crawler

## 階層

src/
├── domains/           # ドメイン層・ビジネスロジック
│   ├── patent.rs      # 特許データ構造体
│   └── designated_states.rs  # 指定国管理
├── handler/           # インフラ層・ユーティリティ
│   ├── html_parser.rs # HTML解析
│   ├── pdf_downloader.rs # PDFダウンロード
│   └── browser_handler.rs # ブラウザ操作
├── jobs/              # アプリケーション層・バッチ処理
│   ├── patent_processor.rs # 特許処理
│   └── sitemap_processor.rs # サイトマップ処理
└── main.rs            # エントリーポイント

## コンテナ

```sh
cd docker

# 開発用
docker compose up --build

# 本番用
docker compose -f docker-compose.prod.yml up --build
```

## テスト

```
curl -o Patentscope_sitemap.xml https://patentscope.wipo.int/sitemap/Patentscope_sitemap.xml
curl -o Patentscope202506.xml https://patentscope.wipo.int/sitemap/en/sitemap_Patentscope202506.xml
```

### browser.rs のテスト

```sh
# tests ファイルにあるサイトマップ、XMLファイル、対象のHTMLを用意すると実行可能
./tests
├── Patentscope202504.xml
├── Patentscope_sitemap.xml
└── WO2025113830.html

DEMO=true IS_LOCAL=true cargo run

# 特定の件数だけスクレイピングする場合
LIMIT=5 cargo run
```
