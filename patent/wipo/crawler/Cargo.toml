[package]
name = "wipo-crawler"
version = "0.1.0"
rust-version = "1.85.0"
edition = "2021"

[dependencies]
reqwest = { version = "0.11", features = ["json", "blocking"] }
tokio = { version = "1", features = ["full"] }
scraper = "0.17"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = "0.4"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
futures = "0.3"
regex = "1.9"
url = "2.4"
pdf-extract = "0.7"
headless_chrome = "1.0.17"
arrow2 = { version = "0.17", features = ["io_parquet"] }
parquet2 = "0.17"
