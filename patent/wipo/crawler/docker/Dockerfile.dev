FROM public.ecr.aws/docker/library/rust:1.86.0-bookworm as builder

WORKDIR /app
COPY . .

RUN cargo build --release --package wipo-crawler

FROM public.ecr.aws/debian/debian:bookworm-20241111-slim

RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# ビルド済みのバイナリをコピー
COPY --from=builder /app/target/release/wipo-crawler /app/wipo-crawler
COPY tests/ /app/tests/

# 環境変数を設定
ENV DEMO=true
ENV IS_LOCAL=true
ENV PATENT_DAYS=7

CMD ["/app/wipo-crawler"]