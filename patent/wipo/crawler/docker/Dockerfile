FROM public.ecr.aws/docker/library/rust:1.86.0-bookworm as builder

WORKDIR /app
COPY . .

RUN cargo build --release --package wipo-crawler

FROM public.ecr.aws/debian/debian:bookworm-20241111-slim

# Chromeとその依存関係をインストール（apt-key非推奨対応）
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    wget \
    gnupg \
    && mkdir -p /etc/apt/keyrings \
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /etc/apt/keyrings/google-chrome.gpg \
    && echo "deb [arch=amd64 signed-by=/etc/apt/keyrings/google-chrome.gpg] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y --no-install-recommends \
    google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY --from=builder /app/target/release/wipo-crawler /app/

ENV DEMO=false
ENV IS_LOCAL=false
ENV PATENT_DAYS=7

CMD ["./wipo-crawler"] 