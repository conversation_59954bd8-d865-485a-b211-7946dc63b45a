/// 許容している出願先のリストを管理する型を定義
#[derive(Debug)]
pub struct DesignatedStates {
    pub states: Vec<String>,
}

/// 新しい指定国リストを作成する
pub fn new_designated_states() -> DesignatedStates {
    DesignatedStates {
        states: vec![
            "JP".to_string(),
            "US".to_string(),
            "CN".to_string(),
            "IN".to_string(),
            "European Patent Office".to_string(),
        ]
    }
}

/// 許容している出願先が含まれているか確認を行う関数
pub fn filter_by_states(designated_states: &DesignatedStates, text: &str) -> bool {
    designated_states.states.iter().any(|state| text.contains(state))
}