/// 特許データを表す構造体
#[derive(Debug, <PERSON><PERSON>)]
#[allow(dead_code)]
pub struct PatentData {
    pub invention_title: String,
    // abstract は予約語のため、abstract_text としている
    pub abstract_text: String, // 概要
    pub claim: String, // 請求項
    pub claims: Vec<String>, // 請求項のリスト
    pub claims_1: String, // 請求項1
    pub pdf_url: Option<String>, // PDFのパス
    pub pct_publication_number: String, // 公開番号
    pub pct_publication_date: String, // 公開日
    pub pct_application_number: String, // 国際出願番号
    pub pct_application_date: String, // 国際出願日
    pub ipc: Vec<String>, // IPC(国際特許分類)
    pub applicants: String, // 出願人
    pub inventor_name: String, // 発明者
    pub priority_data: String, // 優先権情報
    pub lang: String, // 公開言語 = プロダクト側の言語分類で使用
    pub filing_language: String, // 出願言語
    pub designated_states: String, // 出願先
}