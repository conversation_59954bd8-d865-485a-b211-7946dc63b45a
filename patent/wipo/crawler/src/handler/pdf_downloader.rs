use log::info;
use anyhow::Result;
use scraper::{Html, Selector, Element};
use std::io::Write;
use std::path::Path;
use std::fs::File;

/// PDF パスを抽出する関数
pub async fn extract_pdf_url(document: &Html) -> Result<Option<String>> {
    // セクションヘッダーを探す
    let header_selector = Selector::parse("div.ui-datatable-header").unwrap();
    let mut pdf_links = Vec::new();
    
    for header in document.select(&header_selector) {
        let header_text = header.text().collect::<String>();
        
        // "Published International Application" セクションを見つけた場合
        if header_text.contains("Published International Application") {
            // このセクションの親要素を取得
            if let Some(datatable) = header.parent_element() {
                // このセクション内の行を検索
                let row_selector = Selector::parse("tr.ui-widget-content").unwrap();
                
                for row in datatable.select(&row_selector) {
                    // 行内のセルを取得
                    let cell_selector = Selector::parse("td").unwrap();
                    let cells: Vec<_> = row.select(&cell_selector).collect();
                    
                    // タイトルセルを確認 (2番目のセル)
                    if cells.len() >= 2 {
                        let title_cell = &cells[1];
                        let title_text = title_cell.text().collect::<String>();
                        
                        // "Initial Publication with ISR" を含む行を見つけた場合
                        if title_text.contains("Initial Publication with ISR") {
                            // PDFリンクを探す
                            for cell in cells.iter() {
                                let pdf_selector = Selector::parse("a.ps-downloadables").unwrap();
                                
                                for link in cell.select(&pdf_selector) {
                                    let link_text = link.text().collect::<String>();
                                    
                                    if link_text.contains("PDF") {
                                        if let Some(href) = link.value().attr("href") {
                                            // filenameパラメータを含むPDFリンクのみを追加
                                            if href.contains("filename") {
                                                pdf_links.push(href.to_string());
                                                info!("PDFリンクを見つけました: {}", href);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                
                // PDFリンクが見つかった場合、最初のものをダウンロード
                if !pdf_links.is_empty() {
                    let pdf_url = &pdf_links[0];
                    if let Ok(local_path) = download_pdf(pdf_url).await {
                        info!("PDFをダウンロードしました: {}", local_path);
                        return Ok(Some(local_path));
                    } else {
                        info!("PDFのダウンロードに失敗しました: {}", pdf_url);
                        return Ok(None); // ダウンロード失敗時はNoneを返す
                    }
                }
            }
        }
    }

    // PDFリンクが見つからなかった場合
    Ok(None)
}

/// PDFをダウンロードして保存する関数
pub async fn download_pdf(url: &str) -> Result<String, Box<dyn std::error::Error>> {
    use reqwest::Client;

    let file_name = url.split('/').last().unwrap_or("downloaded.pdf");
    let local_path = format!("downloads/{}", file_name);

    if !Path::new("downloads").exists() {
        std::fs::create_dir_all("downloads")?;
    }

    let client = Client::new();
    let response = client.get(url)
        .header("User-Agent", "Mozilla/5.0 ...")
        .send()
        .await?;

    if response.status().is_success() {
        let content = response.bytes().await?;

        let mut file = File::create(&local_path)?;
        file.write_all(&content)?;
        info!("PDFを保存しました: {}", local_path);
        Ok(local_path)
    } else {
        info!("ダウンロード失敗: HTTP {}", response.status());
        Ok(url.to_string())
    }
} 