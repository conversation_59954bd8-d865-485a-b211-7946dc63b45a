use anyhow::Result;
use headless_chrome::{<PERSON><PERSON><PERSON>, Tab};
use log::info;
use std::sync::Arc;

/// 新しいブラウザを作成する
pub fn new_browser() -> Result<Browser> {
    Browser::default()
}

/// 新しいタブを作成して設定する
pub fn create_tab(browser: &Browser) -> Result<Arc<Tab>> {
    let tab = browser.new_tab()?;
    tab.navigate_to("about:blank")?;
    tab.wait_until_navigated()?;
    
    // ユーザーエージェントを設定
    tab.set_user_agent(
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", 
        Some("en-US,en;q=0.9"), 
        Some("macOS")
    )?;
    
    Ok(tab)
}

/// 特許詳細ページにナビゲートする
pub fn navigate_to_patent_detail(browser: &Browser, wo_number: &str) -> Result<Arc<Tab>> {
    let url = format!("https://patentscope2.wipo.int/search/en/detail.jsf?docId={}", wo_number);
    let tab = create_tab(browser)?;
    
    info!("Navigating to patent detail: {}", url);
    tab.navigate_to(&url)?;

    // 特許詳細情報を含む要素が表示されるまで待機
    tab.wait_for_element("div.ps-field-label, span.ps-field--label")?;
    
    Ok(tab)
}

/// Documents タブをクリックしてPDFリンクを取得する
pub fn navigate_to_documents_tab(tab: &mut Arc<Tab>) -> Result<()> {
    let documents_tab_selector = "a[href*='PCTDOCUMENTS']";
    
    if let Ok(documents_tab) = tab.wait_for_element(documents_tab_selector) {
        // タブをクリック
        documents_tab.click()?;
        
        // Documents タブの内容が読み込まれるまで待機
        // タブ切り替え後のコンテンツが表示されるまで少し待機
        std::thread::sleep(std::time::Duration::from_millis(1000));
        
        // PDF リンクが表示されるまで待機
        if let Ok(_) = tab.wait_for_element("a[href$='.pdf']") {
            info!("Documents tab loaded successfully");
            Ok(())
        } else {
            info!("PDF links not found in documents tab");
            Ok(())
        }
    } else {
        info!("Documents tab not found");
        Ok(())
    }
}

/// Claims タブをクリックして請求項情報を取得する
pub fn navigate_to_claims_tab(tab: &mut Arc<Tab>) -> Result<()> {
    let tab_selector = "a[href*='PCTCLAIMS']";
    
    if let Ok(claims_tab) = tab.wait_for_element(tab_selector) {
        info!("Found 'Claims' tab, clicking it.");
        claims_tab.click()?;
        
        // Wait for content to load
        std::thread::sleep(std::time::Duration::from_millis(3000));
        
        if let Ok(_) = tab.wait_for_element("div.claim-text") {
            info!("Claims tab loaded successfully");
        } else {
            info!("Claims content not found after clicking tab.");
        }
    } else {
        info!("'Claims' tab not found.");
    }
    
    Ok(())
}

/// タブを閉じる
pub fn close_tab(tab: Arc<Tab>) -> Result<()> {
    tab.close(true)?;
    Ok(())
} 