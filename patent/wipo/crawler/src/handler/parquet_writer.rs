use crate::domains::PatentData;
use arrow2::array::{Utf8Array, ListArray, Array};
use arrow2::datatypes::{DataType, Field, Schema};
use arrow2::chunk::Chunk;
use arrow2::io::parquet::write::{CompressionOptions, write_file};
use arrow2::offset::Offsets;
use std::fs::File;
use std::sync::Arc;
use anyhow::Result;

pub fn save_patent_data_to_parquet(patents: &[PatentData], path: &str) -> Result<()> {
    // Prepare Arrow arrays for each field
    let invention_title = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.invention_title.as_str()).collect::<Vec<_>>());
    let abstract_text = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.abstract_text.as_str()).collect::<Vec<_>>());
    let claim = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claim.as_str()).collect::<Vec<_>>());
    
    // Create claims list array
    let claims_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.claims.iter().map(|s| s.as_str()))
        .collect();
    let claims_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.claims.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(claims_values.len() as i32))
        .collect();
    let claims_offsets = Offsets::<i32>::try_from(claims_offsets).unwrap();
    let claims = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        claims_offsets.into(),
        Utf8Array::<i32>::from_slice(&claims_values).boxed(),
        None,
    );
    
    let claims_1 = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claims_1.as_str()).collect::<Vec<_>>());
    let pdf_url = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pdf_url.as_deref().unwrap_or("")).collect::<Vec<_>>());
    let pct_publication_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_number.as_str()).collect::<Vec<_>>());
    let pct_publication_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_date.as_str()).collect::<Vec<_>>());
    let pct_application_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_number.as_str()).collect::<Vec<_>>());
    let pct_application_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_date.as_str()).collect::<Vec<_>>());
    
    // Create IPC list array
    let ipc_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.ipc.iter().map(|s| s.as_str()))
        .collect();
    let ipc_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.ipc.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(ipc_values.len() as i32))
        .collect();
    let ipc_offsets = Offsets::<i32>::try_from(ipc_offsets).unwrap();
    let ipc = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        ipc_offsets.into(),
        Utf8Array::<i32>::from_slice(&ipc_values).boxed(),
        None,
    );
    
    let applicants = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.applicants.as_str()).collect::<Vec<_>>());
    let inventor_name = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.inventor_name.as_str()).collect::<Vec<_>>());
    let priority_data = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.priority_data.as_str()).collect::<Vec<_>>());
    let lang = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.lang.as_str()).collect::<Vec<_>>());
    let filing_language = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.filing_language.as_str()).collect::<Vec<_>>());
    let designated_states = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.designated_states.as_str()).collect::<Vec<_>>());

    let schema = Schema::from(vec![
        Field::new("invention_title", DataType::Utf8, false),
        Field::new("abstract_text", DataType::Utf8, false),
        Field::new("claim", DataType::Utf8, false),
        Field::new("claims", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("claims_1", DataType::Utf8, false),
        Field::new("pdf_url", DataType::Utf8, false),
        Field::new("pct_publication_number", DataType::Utf8, false),
        Field::new("pct_publication_date", DataType::Utf8, false),
        Field::new("pct_application_number", DataType::Utf8, false),
        Field::new("pct_application_date", DataType::Utf8, false),
        Field::new("ipc", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("applicants", DataType::Utf8, false),
        Field::new("inventor_name", DataType::Utf8, false),
        Field::new("priority_data", DataType::Utf8, false),
        Field::new("lang", DataType::Utf8, false),
        Field::new("filing_language", DataType::Utf8, false),
        Field::new("designated_states", DataType::Utf8, false),
    ]);

    let chunk = Chunk::try_new(vec![
        Arc::new(invention_title) as Arc<dyn Array>,
        Arc::new(abstract_text),
        Arc::new(claim),
        Arc::new(claims),
        Arc::new(claims_1),
        Arc::new(pdf_url),
        Arc::new(pct_publication_number),
        Arc::new(pct_publication_date),
        Arc::new(pct_application_number),
        Arc::new(pct_application_date),
        Arc::new(ipc),
        Arc::new(applicants),
        Arc::new(inventor_name),
        Arc::new(priority_data),
        Arc::new(lang),
        Arc::new(filing_language),
        Arc::new(designated_states),
    ])?;

    let file = File::create(path)?;
    let mut writer = std::io::BufWriter::new(file);
    write_file(
        &mut writer,
        &chunk,
        Arc::new(schema),
        CompressionOptions::Uncompressed,
        None,
    )?;
    Ok(())
} 