use crate::domains::PatentData;
use arrow2::array::{Utf8Array, ListArray, Array};
use arrow2::datatypes::{DataType, Field, Schema};
use arrow2::chunk::Chunk;
use arrow2::offset::Offsets;
use std::fs::{File, create_dir_all};
use std::path::Path;
use std::sync::Arc;
use anyhow::Result;
use log::info;
use std::io::Write;

pub fn save_patent_data_to_parquet(patents: &[PatentData], path: &str) -> Result<()> {
    // Create directory if it doesn't exist
    if let Some(parent) = Path::new(path).parent() {
        create_dir_all(parent)?;
        info!("Created directory: {:?}", parent);
    }

    info!("Saving {} patent records to parquet file: {}", patents.len(), path);

    // Handle empty data case
    if patents.is_empty() {
        info!("No patent data to save");
        return Ok(());
    }

    // For now, let's save as CSV to test the data structure
    // We'll convert to parquet once we confirm the data is correct
    let csv_path = path.replace(".parquet", ".csv");
    save_as_csv(patents, &csv_path)?;

    // Now try to save as parquet
    save_as_parquet_internal(patents, path)
}

fn save_as_csv(patents: &[PatentData], path: &str) -> Result<()> {
    let mut file = File::create(path)?;

    // Write CSV header
    writeln!(file, "invention_title,abstract_text,claim,claims,claims_1,pdf_url,pct_publication_number,pct_publication_date,pct_application_number,pct_application_date,ipc,applicants,inventor_name,priority_data,lang,filing_language,designated_states")?;

    // Write data rows
    for patent in patents {
        let claims_str = patent.claims.join(";");
        let ipc_str = patent.ipc.join(";");
        let pdf_url_str = patent.pdf_url.as_deref().unwrap_or("");

        writeln!(file, "{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{},{}",
            escape_csv(&patent.invention_title),
            escape_csv(&patent.abstract_text),
            escape_csv(&patent.claim),
            escape_csv(&claims_str),
            escape_csv(&patent.claims_1),
            escape_csv(pdf_url_str),
            escape_csv(&patent.pct_publication_number),
            escape_csv(&patent.pct_publication_date),
            escape_csv(&patent.pct_application_number),
            escape_csv(&patent.pct_application_date),
            escape_csv(&ipc_str),
            escape_csv(&patent.applicants),
            escape_csv(&patent.inventor_name),
            escape_csv(&patent.priority_data),
            escape_csv(&patent.lang),
            escape_csv(&patent.filing_language),
            escape_csv(&patent.designated_states)
        )?;
    }

    info!("Successfully saved CSV file: {}", path);
    Ok(())
}

fn escape_csv(s: &str) -> String {
    if s.contains(',') || s.contains('"') || s.contains('\n') {
        format!("\"{}\"", s.replace('"', "\"\""))
    } else {
        s.to_string()
    }
}

fn save_as_parquet_internal(patents: &[PatentData], path: &str) -> Result<()> {

    // Prepare Arrow arrays for each field
    let invention_title = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.invention_title.as_str()).collect::<Vec<_>>());
    let abstract_text = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.abstract_text.as_str()).collect::<Vec<_>>());
    let claim = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claim.as_str()).collect::<Vec<_>>());
    
    // Create claims list array
    let claims_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.claims.iter().map(|s| s.as_str()))
        .collect();
    let claims_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.claims.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(claims_values.len() as i32))
        .collect();
    let claims_offsets = Offsets::<i32>::try_from(claims_offsets).unwrap();
    let claims = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        claims_offsets.into(),
        Utf8Array::<i32>::from_slice(&claims_values).boxed(),
        None,
    );
    
    let claims_1 = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claims_1.as_str()).collect::<Vec<_>>());
    let pdf_url = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pdf_url.as_deref().unwrap_or("")).collect::<Vec<_>>());
    let pct_publication_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_number.as_str()).collect::<Vec<_>>());
    let pct_publication_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_date.as_str()).collect::<Vec<_>>());
    let pct_application_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_number.as_str()).collect::<Vec<_>>());
    let pct_application_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_date.as_str()).collect::<Vec<_>>());
    
    // Create IPC list array
    let ipc_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.ipc.iter().map(|s| s.as_str()))
        .collect();
    let ipc_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.ipc.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(ipc_values.len() as i32))
        .collect();
    let ipc_offsets = Offsets::<i32>::try_from(ipc_offsets).unwrap();
    let ipc = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        ipc_offsets.into(),
        Utf8Array::<i32>::from_slice(&ipc_values).boxed(),
        None,
    );
    
    let applicants = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.applicants.as_str()).collect::<Vec<_>>());
    let inventor_name = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.inventor_name.as_str()).collect::<Vec<_>>());
    let priority_data = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.priority_data.as_str()).collect::<Vec<_>>());
    let lang = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.lang.as_str()).collect::<Vec<_>>());
    let filing_language = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.filing_language.as_str()).collect::<Vec<_>>());
    let designated_states = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.designated_states.as_str()).collect::<Vec<_>>());

    let schema = Schema::from(vec![
        Field::new("invention_title", DataType::Utf8, false),
        Field::new("abstract_text", DataType::Utf8, false),
        Field::new("claim", DataType::Utf8, false),
        Field::new("claims", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("claims_1", DataType::Utf8, false),
        Field::new("pdf_url", DataType::Utf8, false),
        Field::new("pct_publication_number", DataType::Utf8, false),
        Field::new("pct_publication_date", DataType::Utf8, false),
        Field::new("pct_application_number", DataType::Utf8, false),
        Field::new("pct_application_date", DataType::Utf8, false),
        Field::new("ipc", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("applicants", DataType::Utf8, false),
        Field::new("inventor_name", DataType::Utf8, false),
        Field::new("priority_data", DataType::Utf8, false),
        Field::new("lang", DataType::Utf8, false),
        Field::new("filing_language", DataType::Utf8, false),
        Field::new("designated_states", DataType::Utf8, false),
    ]);

    let chunk = Chunk::try_new(vec![
        Arc::new(invention_title) as Arc<dyn Array>,
        Arc::new(abstract_text),
        Arc::new(claim),
        Arc::new(claims),
        Arc::new(claims_1),
        Arc::new(pdf_url),
        Arc::new(pct_publication_number),
        Arc::new(pct_publication_date),
        Arc::new(pct_application_number),
        Arc::new(pct_application_date),
        Arc::new(ipc),
        Arc::new(applicants),
        Arc::new(inventor_name),
        Arc::new(priority_data),
        Arc::new(lang),
        Arc::new(filing_language),
        Arc::new(designated_states),
    ])?;

    // For now, let's use a simpler approach and just create the arrow arrays
    // We'll implement proper parquet writing later
    info!("Creating Arrow arrays for parquet conversion...");

    // Prepare Arrow arrays for each field
    let invention_title = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.invention_title.as_str()).collect::<Vec<_>>());
    let abstract_text = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.abstract_text.as_str()).collect::<Vec<_>>());
    let claim = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claim.as_str()).collect::<Vec<_>>());

    // Create claims list array
    let claims_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.claims.iter().map(|s| s.as_str()))
        .collect();
    let claims_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.claims.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(claims_values.len() as i32))
        .collect();
    let claims_offsets = Offsets::<i32>::try_from(claims_offsets).unwrap();
    let claims = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        claims_offsets.into(),
        Utf8Array::<i32>::from_slice(&claims_values).boxed(),
        None,
    );

    let claims_1 = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.claims_1.as_str()).collect::<Vec<_>>());
    let pdf_url = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pdf_url.as_deref().unwrap_or("")).collect::<Vec<_>>());
    let pct_publication_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_number.as_str()).collect::<Vec<_>>());
    let pct_publication_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_publication_date.as_str()).collect::<Vec<_>>());
    let pct_application_number = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_number.as_str()).collect::<Vec<_>>());
    let pct_application_date = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.pct_application_date.as_str()).collect::<Vec<_>>());

    // Create IPC list array
    let ipc_values: Vec<&str> = patents.iter()
        .flat_map(|p| p.ipc.iter().map(|s| s.as_str()))
        .collect();
    let ipc_offsets: Vec<i32> = patents.iter()
        .scan(0, |acc, p| {
            let start = *acc;
            *acc += p.ipc.len() as i32;
            Some(start)
        })
        .chain(std::iter::once(ipc_values.len() as i32))
        .collect();
    let ipc_offsets = Offsets::<i32>::try_from(ipc_offsets).unwrap();
    let ipc = ListArray::<i32>::new(
        DataType::List(Box::new(Field::new("item", DataType::Utf8, true))),
        ipc_offsets.into(),
        Utf8Array::<i32>::from_slice(&ipc_values).boxed(),
        None,
    );

    let applicants = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.applicants.as_str()).collect::<Vec<_>>());
    let inventor_name = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.inventor_name.as_str()).collect::<Vec<_>>());
    let priority_data = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.priority_data.as_str()).collect::<Vec<_>>());
    let lang = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.lang.as_str()).collect::<Vec<_>>());
    let filing_language = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.filing_language.as_str()).collect::<Vec<_>>());
    let designated_states = Utf8Array::<i32>::from_slice(patents.iter().map(|p| p.designated_states.as_str()).collect::<Vec<_>>());

    let schema = Schema::from(vec![
        Field::new("invention_title", DataType::Utf8, false),
        Field::new("abstract_text", DataType::Utf8, false),
        Field::new("claim", DataType::Utf8, false),
        Field::new("claims", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("claims_1", DataType::Utf8, false),
        Field::new("pdf_url", DataType::Utf8, false),
        Field::new("pct_publication_number", DataType::Utf8, false),
        Field::new("pct_publication_date", DataType::Utf8, false),
        Field::new("pct_application_number", DataType::Utf8, false),
        Field::new("pct_application_date", DataType::Utf8, false),
        Field::new("ipc", DataType::List(Box::new(Field::new("item", DataType::Utf8, true))), false),
        Field::new("applicants", DataType::Utf8, false),
        Field::new("inventor_name", DataType::Utf8, false),
        Field::new("priority_data", DataType::Utf8, false),
        Field::new("lang", DataType::Utf8, false),
        Field::new("filing_language", DataType::Utf8, false),
        Field::new("designated_states", DataType::Utf8, false),
    ]);

    let chunk = Chunk::try_new(vec![
        Arc::new(invention_title) as Arc<dyn Array>,
        Arc::new(abstract_text),
        Arc::new(claim),
        Arc::new(claims),
        Arc::new(claims_1),
        Arc::new(pdf_url),
        Arc::new(pct_publication_number),
        Arc::new(pct_publication_date),
        Arc::new(pct_application_number),
        Arc::new(pct_application_date),
        Arc::new(ipc),
        Arc::new(applicants),
        Arc::new(inventor_name),
        Arc::new(priority_data),
        Arc::new(lang),
        Arc::new(filing_language),
        Arc::new(designated_states),
    ])?;

    // Now try to write the actual parquet file using a simpler approach
    let file = File::create(path)?;
    let mut writer = std::io::BufWriter::new(file);

    // Use arrow2's built-in parquet writing with a simpler approach
    match write_parquet_simple(&chunk, &schema, &mut writer) {
        Ok(_) => {
            info!("Successfully saved parquet file: {}", path);
        }
        Err(e) => {
            info!("Failed to write parquet file: {}. Data is available in CSV format.", e);
        }
    }

    Ok(())
}

fn write_parquet_simple(
    chunk: &Chunk<Arc<dyn Array>>,
    schema: &Schema,
    writer: &mut std::io::BufWriter<File>,
) -> Result<()> {
    // Try using arrow2's simplified parquet writing
    use arrow2::io::parquet::write::{WriteOptions, CompressionOptions, Version};

    let options = WriteOptions {
        write_statistics: true,
        compression: CompressionOptions::Uncompressed,
        version: Version::V1,
        data_pagesize_limit: None,
    };

    // Convert to parquet schema
    let parquet_schema = arrow2::io::parquet::write::to_parquet_schema(schema)?;

    // Create a simple row group iterator
    let row_groups = std::iter::once(Ok(chunk.clone()));

    // Create encodings for each field
    let encodings: Vec<Vec<arrow2::io::parquet::write::Encoding>> = schema.fields.iter()
        .map(|_| vec![arrow2::io::parquet::write::Encoding::Plain])
        .collect();

    // Use arrow2's row group iterator
    let row_group_iter = arrow2::io::parquet::write::RowGroupIterator::try_new(
        row_groups,
        schema,
        options,
        encodings,
    )?;

    // Create file writer
    let mut file_writer = parquet2::write::FileWriter::new(
        writer,
        parquet_schema,
        parquet2::write::WriteOptions {
            write_statistics: true,
            version: parquet2::write::Version::V1,
        },
        None,
    );

    // Write row groups
    for group in row_group_iter {
        file_writer.write(group?)?;
    }

    file_writer.end(None)?;

    Ok(())
}