use scraper::{Html, Selector, Element};
use regex::Regex;
use crate::domains::PatentData;

/// HTMLドキュメントから特許データを抽出する関数
pub fn extract_patent_data_from_document(document: &Html) -> PatentData {
    let invention_title = extract_attribute_from_document(&document, "meta[name='keywords']", "content")
        .unwrap_or_else(|| "None".to_string());

    let abstract_text = extract_attribute_from_document(&document, "meta[name='description']", "content")
        .unwrap_or_else(|| "None".to_string());
    
    let pct_publication_number = extract_field_value(&document, "Publication Number")
        .unwrap_or_else(|| "None".to_string());
    
    let pct_publication_date = extract_field_value(&document, "Publication Date")
        .unwrap_or_else(|| "None".to_string());
    
    let pct_application_number = extract_field_value(&document, "International Application No.")
        .unwrap_or_else(|| "None".to_string());
    
    let pct_application_date = extract_field_value(&document, "International Filing Date")
        .unwrap_or_else(|| "None".to_string());

    let ipc = extract_ipc_classifications(&document);
    
    let applicants = extract_field_value(&document, "Applicants")
        .unwrap_or_else(|| "None".to_string());
    
    let inventor_name = extract_field_value(&document, "Inventors")
        .unwrap_or_else(|| "None".to_string());
    
    let priority_data = extract_field_value(&document, "Priority Data")
        .unwrap_or_else(|| "None".to_string());
    
    let lang = extract_field_value(&document, "Publication Language")
        .unwrap_or_else(|| "None".to_string());
    
    let filing_language = extract_field_value(&document, "Filing Language")
        .unwrap_or_else(|| "None".to_string());
    
    let designated_states = extract_field_value(&document, "Designated States")
        .unwrap_or_else(|| "None".to_string());

    // PatentData構造体を作成して返す
    PatentData {
        invention_title,
        abstract_text,
        claim: String::new(),
        claims: Vec::new(),
        claims_1: String::new(),
        pdf_url: None, // PDFのパスはここでは取得しない
        pct_publication_number,
        pct_publication_date,
        pct_application_number,
        pct_application_date,
        ipc,
        applicants,
        inventor_name,
        priority_data,
        lang,
        filing_language,
        designated_states,
    }
}

/// HTMLドキュメントから特定の要素の属性を抽出する補助関数
pub fn extract_attribute_from_document(document: &Html, selector_str: &str, attr: &str) -> Option<String> {
    let selector = match Selector::parse(selector_str) {
        Ok(selector) => selector,
        Err(_) => return None,
    };
    
    document.select(&selector).next()
        .and_then(|element| element.value().attr(attr))
        .map(|s| s.to_string())
}

/// IPCの分類情報を抽出する関数
pub fn extract_ipc_classifications(document: &Html) -> Vec<String> {
    let mut ipc_list = Vec::new();
    let mut seen_ipcs = std::collections::HashSet::new(); // 重複チェック用のHashSet
    
    // patent-classification クラスを持つ div 要素を選択
    let div_selector = Selector::parse("div.patent-classification").unwrap();
    
    for div in document.select(&div_selector) {
        // 各 div 内の PCTipc span を選択
        if let Some(ipc_span) = div.select(&Selector::parse("span[id$='PCTipc']").unwrap()).next() {
            // IPC コードを含む a タグを選択
            if let Some(a_tag) = ipc_span.select(&Selector::parse("a").unwrap()).next() {
                let ipc_code = a_tag.text().collect::<String>();
                
                // バージョン情報を含む span を選択
                if let Some(version_span) = ipc_span.select(&Selector::parse("span:last-child").unwrap()).next() {
                    let version = version_span.text().collect::<String>();
                    
                    // IPC コードとバージョンを組み合わせる
                    let formatted_ipc = format!("{} {}", ipc_code.trim(), version.trim());
                    
                    // 重複チェック: まだ見ていないIPCの場合のみ追加
                    if seen_ipcs.insert(formatted_ipc.clone()) {
                        ipc_list.push(formatted_ipc);
                    }
                }
            }
        }
    }
    
    ipc_list
}

/// フィールド名からフィールド値を抽出する関数
pub fn extract_field_value(document: &Html, field_name: &str) -> Option<String> {
    // フィールド名を含む要素を探す - spanタグも含める
    let field_selector = Selector::parse("div.ps-field-label, th, span.ps-field--label").ok()?;
    let value_selector = Selector::parse("span.ps-field--value, div.ps-field-value, td").ok()?;
    
    for element in document.select(&field_selector) {
        let text = element.text().collect::<Vec<_>>().join(" ").trim().to_string();
        if text.contains(field_name) {
            // 同じ親要素内の値を持つspan要素を探す
            if let Some(parent_element) = element.parent_element() {
                // 親要素のIDを取得
                if let Some(parent_id) = parent_element.value().attr("id") {
                    // 同じ親ID内の値要素を探す
                    for value_element in document.select(&value_selector) {
                        if let Some(value_parent) = value_element.parent_element() {
                            if value_parent.value().attr("id") == Some(parent_id) {
                                let raw_text = value_element.text().collect::<Vec<_>>().join(" ").trim().to_string();
                                return Some(clean_text(&raw_text));
                            }
                        }
                    }
                }
                
                // 親要素内の値要素を直接探す
                for value_element in document.select(&value_selector) {
                    if value_element.parent_element() == Some(parent_element) {
                        let raw_text = value_element.text().collect::<Vec<_>>().join(" ").trim().to_string();
                        return Some(clean_text(&raw_text));
                    }
                }
            }
            
            // 兄弟要素を探す方法
            if let Some(next_sibling) = element.next_sibling_element() {
                if (next_sibling.value().name() == "div" && next_sibling.value().attr("class").map_or(false, |c| c.contains("ps-field-value"))) || (next_sibling.value().name() == "span" && next_sibling.value().attr("class").map_or(false, |c| c.contains("ps-field--value"))) {
                    let raw_text = next_sibling.text().collect::<Vec<_>>().join(" ").trim().to_string();
                    return Some(clean_text(&raw_text));
                }
            }
            
            // 特定のケース: 同じdiv内のラベルと値のspan
            // 提供されたHTMLパターンに対応
            if element.value().name() == "span" && element.value().attr("class").map_or(false, |c| c.contains("ps-field--label")) {
                if let Some(next_sibling) = element.next_sibling_element() {
                    if next_sibling.value().name() == "span" && next_sibling.value().attr("class").map_or(false, |c| c.contains("ps-field--value")) {
                        let raw_text = next_sibling.text().collect::<Vec<_>>().join(" ").trim().to_string();
                        return Some(clean_text(&raw_text));
                    }
                }
            }
        }
    }
    
    None
}

/// テキストをクリーンアップする関数
pub fn clean_text(text: &str) -> String {
    // 複数の空白、タブ、改行を単一の空白に置き換え
    let re = Regex::new(r"\\s+").unwrap();
    let cleaned = re.replace_all(text.trim(), " ");
    cleaned.to_string()
}

/// 請求項データを抽出する関数
pub fn extract_claims_data(document: &Html) -> (String, Vec<String>, String) {
    let claims_selector = Selector::parse("div.claim-text").unwrap();
    let claims_nodes = document.select(&claims_selector);

    let claims_list: Vec<String> = claims_nodes
        .map(|node| node.text().collect::<String>().trim().to_string())
        .filter(|s| !s.is_empty())
        .collect();

    let all_claims = claims_list.join("\\n");
    let first_claim = claims_list.get(0).cloned().unwrap_or_default();

    (all_claims, claims_list, first_claim)
} 