pub mod browser_handler;
pub mod html_parser;
pub mod pdf_downloader;
pub mod parquet_writer;

pub use self::{
    browser_handler::{
        close_tab, new_browser, navigate_to_claims_tab, navigate_to_documents_tab,
        navigate_to_patent_detail,
    },
    html_parser::{
        clean_text, extract_claims_data, extract_patent_data_from_document,
    },
    pdf_downloader::{download_pdf, extract_pdf_url},
}; 