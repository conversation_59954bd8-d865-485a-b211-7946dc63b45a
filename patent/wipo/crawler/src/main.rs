mod domains;
mod handler;
mod jobs;

use anyhow::Result;
use log::info;
use reqwest::Client;
use std::env;
use std::fs;

use jobs::{fetch_sitemap, parse_sitemap_content, filter_recent_entries, extract_wo_numbers_from_demo_file, extract_wo_numbers_from_urls, process_wo_numbers};
use handler::{parquet_writer::save_patent_data_to_parquet};

#[tokio::main]
async fn main() -> Result<()> {
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    let is_demo = env::var("DEMO").unwrap_or_default() == "true";
    let is_local = env::var("IS_LOCAL").unwrap_or_default() == "true";
    let client = Client::builder() 
        .user_agent("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36")
        .build()?;
    
    // サイトマップのエントリをフィルタリング
    let sitemap_data = if is_demo {
        // DEMOモードの場合はローカルのファイルを使用
        info!("Running in DEMO mode, using local sitemap file");
        let local_path = "./tests/Patentscope_sitemap.xml";
        let content = fs::read_to_string(local_path)?;
        parse_sitemap_content(&content)?
    } else {
        let sitemap_url = "https://patentscope.wipo.int/sitemap/Patentscope_sitemap.xml";
        fetch_sitemap(&client, sitemap_url).await?
    };
    let days = env::var("PATENT_DAYS")
        .ok()
        .and_then(|s| s.parse::<i64>().ok())
        .unwrap_or(7);
    let recent_urls = filter_recent_entries(sitemap_data, days).await?;
    
    // フィルタリングされたURLからWO番号を抽出
    let mut wo_numbers: Vec<String>;
    if is_demo {
        let test_file_path = "./tests/Patentscope202506.xml";
        wo_numbers = extract_wo_numbers_from_demo_file(test_file_path).await?;
    } else {
        wo_numbers = extract_wo_numbers_from_urls(&client, recent_urls).await?;
    };
    info!("Successfully extracted {} WO numbers", wo_numbers.len());

    // 取得したWO番号ごとにデータを取得する
    if !wo_numbers.is_empty() {
        // LIMIT 環境変数が設定されている場合、指定された数だけ処理する
        if let Ok(wo_numbers_str) = env::var("LIMIT") {
            if let Ok(limit) = wo_numbers_str.parse::<usize>() {
                info!("Processing only the first {} WO numbers as specified by LIMIT", limit);
                wo_numbers.truncate(limit);
            }
        }
        if is_demo {
            // DEMOモードの場合は、最初の1件のみ処理
            let wo_numbers_to_process = vec![wo_numbers[0].clone()];
            let results = process_wo_numbers(wo_numbers_to_process, is_local).await?;
            save_patent_data_to_parquet(&results, "output/patents.parquet")?;
        } else {
            let results = process_wo_numbers(wo_numbers, is_local).await?;
            // TODO: S3 に保存
            save_patent_data_to_parquet(&results, "output/patents.parquet")?;
        }
    } else {
        info!("No WO numbers to process");
    }
    
    Ok(())
}
