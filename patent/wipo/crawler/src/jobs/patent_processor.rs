use log::info;
use std::fs;
use anyhow::Result;
use scraper::Html;
use crate::domains::{PatentData, DesignatedStates, new_designated_states, filter_by_states};
use crate::handler::{extract_patent_data_from_document, extract_pdf_url, new_browser, navigate_to_patent_detail, navigate_to_documents_tab, close_tab, extract_claims_data, navigate_to_claims_tab};

/// 複数のWO番号を処理する関数
pub async fn process_wo_numbers(wo_numbers: Vec<String>, is_local: bool) -> Result<Vec<PatentData>> {
    info!("処理するWO番号: {} 件", wo_numbers.len());
    let total_count = wo_numbers.len();
    
    // 指定国リストを初期化
    let designated_states_list = new_designated_states();
    
    let (results, skipped_count) = if is_local {
        // LOCALモードの場合、ブラウザを立ち上げずにローカルのHTMLファイルを使用
        process_wo_numbers_local(wo_numbers, &designated_states_list).await?
    } else {
        // 通常モード: ブラウザを使用
        process_wo_numbers_with_browser(wo_numbers, &designated_states_list).await?
    };
    
    // 結果の集計
    let success_count = results.len();
    let error_count = total_count - success_count - skipped_count;
    
    // 対象外の件数も含めて結果を出力
    info!("処理完了: 成功={}, 対象外={}, 失敗={}", success_count, skipped_count, error_count);
    
    Ok(results)
}

/// ローカルモードでのWO番号処理
async fn process_wo_numbers_local(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {
    let mut results = Vec::new();
    let mut skipped_count = 0;

    for wo_number in wo_numbers {
        let sample_html_file = format!("./tests/WO2025113830.html");
        info!("DEMOモード: ファイル {} を読み込みます", sample_html_file);
        
        let html = fs::read_to_string(&sample_html_file).unwrap();
        let document = Html::parse_document(&html);
        let mut patent_data = extract_patent_data_from_document(&document);

        // 指定国のいずれかが含まれている場合のみ結果に追加
        if filter_by_states(designated_states_list, &patent_data.designated_states) {
            // Claimsデータを抽出
            let claims_html_file = format!("./tests/WO2025113830_claims.html");
            let claims_html = fs::read_to_string(&claims_html_file).unwrap();
            let claims_document = Html::parse_document(&claims_html);
            let (claim, claims, claims_1) = extract_claims_data(&claims_document);
            patent_data.claim = claim;
            patent_data.claims = claims;
            patent_data.claims_1 = claims_1;
            info!("WO番号 {} の Claims データを追加しました", wo_number);

            // PDFパスを抽出
            let sample_pdf_file = format!("./tests/WO2025113830_documents.html");
            let pdf_html = fs::read_to_string(&sample_pdf_file).unwrap();
            let pdf_document = Html::parse_document(&pdf_html);
            patent_data.pdf_url = extract_pdf_url(&pdf_document).await?;
            info!("WO番号 {} の PDF パス: {:?}", wo_number, patent_data.pdf_url);
            results.push(patent_data);
        } else {
            info!("WO番号 {} は指定国に含まれていないためスキップします", wo_number);
            skipped_count += 1;  // 対象外としてカウント
        }
    }

    Ok((results, skipped_count))
}

/// ブラウザモードでのWO番号処理
async fn process_wo_numbers_with_browser(wo_numbers: Vec<String>, designated_states_list: &DesignatedStates) -> Result<(Vec<PatentData>, usize)> {
    let mut results = Vec::new();
    let mut skipped_count = 0;

    let browser = new_browser()?;

    for wo_number in wo_numbers {
        let mut tab = navigate_to_patent_detail(&browser, &wo_number)?;
        
        let html = tab.get_content()?;
        let document = Html::parse_document(&html);
        let mut patent_data = extract_patent_data_from_document(&document);
        
        // 指定国のいずれかが含まれている場合のみ結果に追加
        if filter_by_states(designated_states_list, &patent_data.designated_states) {
            // Claims タブが存在するか確認してクリック
            navigate_to_claims_tab(&mut tab)?;

            // Claims データを抽出
            if let Ok(_) = tab.wait_for_element("div.claim-text") {
                let updated_html = tab.get_content()?;
                let updated_document = Html::parse_document(&updated_html);
                let (claim, claims, claims_1) = extract_claims_data(&updated_document);
                patent_data.claim = claim;
                patent_data.claims = claims;
                patent_data.claims_1 = claims_1;
                info!("WO番号 {} の Claims データを追加しました", wo_number);
            } else {
                info!("WO番号 {} の Claims データが見つかりませんでした", wo_number);
            }

            // Documents タブが存在するか確認してクリック
            navigate_to_documents_tab(&mut tab)?;
            
            // PDF リンクが表示されるまで待機
            if let Ok(_) = tab.wait_for_element("a[href$='.pdf']") {
                let updated_html = tab.get_content()?;
                let updated_document = Html::parse_document(&updated_html);
                patent_data.pdf_url = extract_pdf_url(&updated_document).await?;
                info!("WO番号 {} の PDF パス: {:?}", wo_number, patent_data.pdf_url);
            } else {
                info!("WO番号 {} の PDF リンクが見つかりませんでした", wo_number);
            }

            results.push(patent_data);
        } else {
            info!("WO番号 {} は指定国に含まれていないためスキップします", wo_number);
            skipped_count += 1;  // 対象外としてカウント
        }

        close_tab(tab)?;
    }

    Ok((results, skipped_count))
} 