use anyhow::Result;
use chrono::{DateTime, Duration, Utc};
use log::info;
use regex::Regex;
use reqwest::Client;
use scraper::{Html, Selector};
use serde::Deserialize;
use std::time::Duration as StdDuration;
use std::fs;

// サイトマップのモデル
#[derive(Debug, Deserialize)]
pub struct SitemapEntry {
    pub loc: String,
    pub lastmod: String,
}

#[derive(Debug, Deserialize)]
pub struct Sitemap {
    #[serde(rename = "sitemap", default)]
    pub entries: Vec<SitemapEntry>,
}

/// サイトマップ取得関数
pub async fn fetch_sitemap(client: &Client, url: &str) -> Result<Sitemap> {
    info!("Fetching sitemap from {}", url);
    let response = client
        .get(url)
        .timeout(StdDuration::from_secs(30))
        .send()
        .await?
        .text()
        .await?;

    parse_sitemap_content(&response)
}

/// サイトマップパース関数
pub fn parse_sitemap_content(content: &str) -> Result<Sitemap> {
    let document = Html::parse_document(content);
    
    let sitemap_selector = Selector::parse("sitemap").unwrap();
    let loc_selector = Selector::parse("loc").unwrap();
    let lastmod_selector = Selector::parse("lastmod").unwrap();
    
    let mut entries = Vec::new();
    
    let sitemap_elements: Vec<_> = document.select(&sitemap_selector).collect();
    info!("Found {} sitemap elements in sitemapindex", sitemap_elements.len());
    
    for sitemap_element in sitemap_elements {
        let loc = sitemap_element
            .select(&loc_selector)
            .next()
            .and_then(|e| Some(e.inner_html()))
            .unwrap_or_default();
            
        let lastmod = sitemap_element
            .select(&lastmod_selector)
            .next()
            .and_then(|e| Some(e.inner_html()))
            .unwrap_or_default();
        
        entries.push(SitemapEntry { loc, lastmod });
    }
    
    Ok(Sitemap { entries })
}

/// 最近のエントリをフィルタリングする関数
pub async fn filter_recent_entries(sitemap: Sitemap, days: i64) -> Result<Vec<String>> {
    let now = Utc::now().date_naive().and_hms_opt(0, 0, 0).unwrap();
    let now = DateTime::<Utc>::from_naive_utc_and_offset(now, Utc);
    let cutoff_date = now - Duration::days(days);
    info!("Filtering entries newer than {}", cutoff_date);
    
    let mut recent_urls = Vec::new();
    
    for entry in sitemap.entries {
        let parse_result = chrono::NaiveDate::parse_from_str(&entry.lastmod, "%Y-%m-%d");
        
        match parse_result {
            Ok(date) => {
                let datetime = date.and_hms_opt(0, 0, 0).unwrap();
                let utc_datetime = DateTime::<Utc>::from_naive_utc_and_offset(datetime, Utc);
                if utc_datetime >= cutoff_date {
                    recent_urls.push(entry.loc);
                }
            },
            Err(e) => {
                info!("Failed to parse date '{}': {}", entry.lastmod, e);
            }
        }
    }
    
    info!("Found {} recent sitemap entries", recent_urls.len());
    Ok(recent_urls)
}

/// デモモード用にローカルファイルからWO番号を抽出する関数
pub async fn extract_wo_numbers_from_demo_file(file_path: &str) -> Result<Vec<String>> {
    let re = Regex::new(r"WO\d+$")?;
    info!("DEMO mode: Using local test file: {}", file_path);
    let content = match fs::read_to_string(file_path) {
        Ok(content) => content,
        Err(e) => {
            info!("Failed to read test file {}: {}", file_path, e);
            return Ok(Vec::new());
        }
    };
    
    let document = Html::parse_document(&content);
    let loc_selector = Selector::parse("loc").unwrap();
    
    let mut wo_numbers = Vec::new();
    for loc_element in document.select(&loc_selector) {
        let loc_url = loc_element.inner_html();
        if let Some(m) = re.find(&loc_url) {
            wo_numbers.push(m.as_str().to_string());
        }
    }
    
    info!("Extracted {} WO numbers from demo file", wo_numbers.len());
    Ok(wo_numbers)
}

/// 実際のURLからWO番号を抽出する関数
pub async fn extract_wo_numbers_from_urls(client: &Client, urls: Vec<String>) -> Result<Vec<String>> {
    let re = Regex::new(r"WO\d+$")?;
    let mut wo_numbers = Vec::new();
    
    for url in urls {
        info!("Fetching sitemap content from {}", url);
        let response = match client
            .get(&url)
            .timeout(StdDuration::from_secs(30))
            .send()
            .await {
                Ok(resp) => resp,
                Err(e) => {
                    info!("Failed to fetch sitemap {}: {}", url, e);
                    continue;
                }
            };
            
        let content = match response.text().await {
            Ok(text) => text,
            Err(e) => {
                info!("Failed to read response from {}: {}", url, e);
                continue;
            }
        };
        
        let document = Html::parse_document(&content);
        let loc_selector = Selector::parse("loc").unwrap();
        
        for loc_element in document.select(&loc_selector) {
            let loc_url = loc_element.inner_html();
            if let Some(m) = re.find(&loc_url) {
                wo_numbers.push(m.as_str().to_string());
            }
        }
    }
    
    info!("Extracted {} WO numbers from URLs", wo_numbers.len());
    Ok(wo_numbers)
} 